# Sanrado Website

This is the official website for Sanrado, built with React, TypeScript, and Vite. The website showcases our technology solutions for small and medium businesses.

## Features

- ⚡ **Fast & Modern**: Built with Vite for lightning-fast development and optimized builds
- 🎨 **Tailwind CSS**: Beautiful, responsive design with Tailwind CSS v4
- 🔒 **TypeScript**: Full type safety throughout the application
- 📱 **Responsive**: Mobile-first design that works on all devices
- ♿ **Accessible**: WCAG compliant with skip navigation, proper ARIA labels, and semantic HTML
- 🚀 **Performance**: Code splitting, lazy loading, and optimized bundle sizes
- 🛡️ **Error Boundaries**: Graceful error handling with user-friendly error pages
- 🔍 **SEO Optimized**: Dynamic meta tags, Open Graph, and Twitter Card support

## Recent Improvements

- ✅ Updated branding from TechEnabler to Sanrado
- ✅ Added comprehensive SEO meta tags and Open Graph support
- ✅ Implemented error boundaries for better error handling
- ✅ Added loading states and code splitting for improved performance
- ✅ Created environment configuration system
- ✅ Improved accessibility with skip navigation and ARIA labels
- ✅ Enhanced Button component with loading states and external link support
- ✅ Added mobile menu backdrop and improved touch interactions

## Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Lint code
npm run lint
```

## Environment Variables

Copy `.env.example` to `.env` and configure as needed:

```bash
cp .env.example .env
```

## Project Structure

```
src/
├── components/
│   ├── ErrorBoundary.tsx     # Error boundary component
│   ├── Layout/               # Layout components
│   ├── SEO/                  # SEO and meta tag components
│   └── UI/                   # Reusable UI components
├── pages/                    # Page components
├── utils/                    # Utility functions
└── types/                    # TypeScript type definitions
```
