# Contact Form Email Setup - Amazon SES (Recommended) + Alternative Options

This guide covers setting up email functionality for your contact form with **Amazon SES** as the primary recommendation, offering the most generous free tier and cost-effective scaling.

## 🏆 **RECOMMENDED: Amazon SES Setup**

Amazon SES offers **3,000 emails/month free** for 12 months, then $0.10 per 1,000 emails - making it the most cost-effective solution.

### Option A: Server-Side Implementation (Most Secure)

Create a simple Node.js/Express backend to handle email sending securely.

#### 1. Create AWS Account & Set Up SES

1. Go to [AWS Console](https://aws.amazon.com/)
2. Create an AWS account (if you don't have one)
3. Navigate to **Amazon SES** service
4. **Verify your email domain or address:**
   - Go to "Configuration" > "Verified identities"
   - Click "Create identity"
   - Choose "Domain" and enter your domain name
   - Follow DNS verification steps
5. **Request production access:**
   - Go to "Account dashboard"
   - Click "Request production access"
   - Fill out the form with your use case

#### 2. Get AWS Credentials

1. Go to **IAM** service in AWS Console
2. Create a new user with programmatic access
3. Attach the `AmazonSESFullAccess` policy
4. Save the **Access Key ID** and **Secret Access Key**

#### 3. Create Backend Service

Create `server/` directory in your project:

```javascript
// server/index.js
const express = require("express");
const cors = require("cors");
const { SESClient, SendEmailCommand } = require("@aws-sdk/client-ses");

const app = express();
app.use(cors());
app.use(express.json());

const sesClient = new SESClient({
  region: "us-east-1", // or your preferred region
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

app.post("/api/send-email", async (req, res) => {
  try {
    const { name, email, company, phone, subject, message, priority } =
      req.body;

    const emailParams = {
      Source: "<EMAIL>", // Must be verified in SES
      Destination: {
        ToAddresses: ["<EMAIL>"],
      },
      Message: {
        Subject: {
          Data: `New Contact Form Submission - ${subject}`,
          Charset: "UTF-8",
        },
        Body: {
          Html: {
            Data: `
              <h2>New Contact Form Submission</h2>
              <p><strong>From:</strong> ${name} (${email})</p>
              <p><strong>Company:</strong> ${company}</p>
              <p><strong>Phone:</strong> ${phone}</p>
              <p><strong>Priority:</strong> ${priority}</p>
              <p><strong>Subject:</strong> ${subject}</p>
              <p><strong>Message:</strong></p>
              <p>${message}</p>
              <hr>
              <p><em>This email was sent from the Sanrado Techsolutions LLP contact form.</em></p>
            `,
            Charset: "UTF-8",
          },
        },
      },
    };

    const command = new SendEmailCommand(emailParams);
    await sesClient.send(command);

    res.json({ success: true, message: "Email sent successfully" });
  } catch (error) {
    console.error("Error sending email:", error);
    res.status(500).json({ success: false, error: "Failed to send email" });
  }
});

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
```

#### 4. Update Frontend Contact Form

```typescript
// Update the handleSubmit function in src/pages/Contact.tsx
const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  setFormState((prev) => ({ ...prev, loading: true, error: "" }));

  try {
    const response = await fetch("/api/send-email", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        name: formState.name,
        email: formState.email,
        company: formState.company,
        phone: formState.phone,
        subject: formState.subject,
        message: formState.message,
        priority: formState.priority,
      }),
    });

    const result = await response.json();

    if (result.success) {
      setFormState((prev) => ({
        ...prev,
        submitted: true,
        loading: false,
      }));

      // Reset form after 5 seconds
      setTimeout(() => {
        setFormState({
          name: "",
          email: "",
          company: "",
          phone: "",
          subject: "",
          message: "",
          priority: "normal",
          submitted: false,
          loading: false,
          error: "",
        });
      }, 5000);
    } else {
      throw new Error(result.error);
    }
  } catch (error) {
    console.error("Error:", error);
    setFormState((prev) => ({
      ...prev,
      loading: false,
      error:
        "Failed to send message. Please try again or contact us <NAME_EMAIL>",
    }));
  }
};
```

#### 5. Environment Variables

```env
# Server environment variables
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1

# Frontend environment variables (if needed)
VITE_API_URL=http://localhost:3001
```

### Option B: Client-Side with AWS SDK (Less Secure)

If you prefer client-side implementation:

```typescript
// Install: npm install @aws-sdk/client-ses

import { SESClient, SendEmailCommand } from "@aws-sdk/client-ses";

const sesClient = new SESClient({
  region: "us-east-1",
  credentials: {
    accessKeyId: import.meta.env.VITE_AWS_ACCESS_KEY_ID,
    secretAccessKey: import.meta.env.VITE_AWS_SECRET_ACCESS_KEY,
  },
});

// Use in your handleSubmit function
const command = new SendEmailCommand(emailParams);
await sesClient.send(command);
```

## 🥈 **Alternative: SendGrid (Backup Option)**

If AWS seems too complex, SendGrid offers easier setup:

### 1. Create SendGrid Account

1. Go to [SendGrid.com](https://sendgrid.com/)
2. Sign up for free trial (100 emails/day for 60 days)
3. Get your API key from Settings > API Keys

### 2. Install and Use

```bash
npm install @sendgrid/mail
```

```typescript
import sgMail from "@sendgrid/mail";

sgMail.setApiKey(import.meta.env.VITE_SENDGRID_API_KEY);

const msg = {
  to: "<EMAIL>",
  from: "<EMAIL>", // Must be verified
  subject: `New Contact Form Submission - ${formState.subject}`,
  html: `
    <h2>New Contact Form Submission</h2>
    <p><strong>From:</strong> ${formState.name} (${formState.email})</p>
    <p><strong>Company:</strong> ${formState.company}</p>
    <p><strong>Phone:</strong> ${formState.phone}</p>
    <p><strong>Priority:</strong> ${formState.priority}</p>
    <p><strong>Message:</strong></p>
    <p>${formState.message}</p>
  `,
};

await sgMail.send(msg);
```

## 🥉 **Current EmailJS Setup (Keep as Fallback)**

Your current EmailJS setup can remain as a fallback option. Update the environment variables to use the new services:

```env
# Environment Variables (.env)

# Primary: Amazon SES
VITE_USE_SES=true
VITE_AWS_ACCESS_KEY_ID=your_aws_access_key
VITE_AWS_SECRET_ACCESS_KEY=your_aws_secret_key

# Secondary: SendGrid
VITE_SENDGRID_API_KEY=your_sendgrid_api_key

# Fallback: EmailJS
VITE_EMAILJS_PUBLIC_KEY=your_emailjs_public_key
VITE_EMAILJS_SERVICE_ID=your_emailjs_service_id
VITE_EMAILJS_TEMPLATE_ID=your_emailjs_template_id

# Google Maps
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
```

## Cost Comparison Summary

| Provider       | Free Tier               | Cost After Free    | Best For                    |
| -------------- | ----------------------- | ------------------ | --------------------------- |
| **Amazon SES** | 3,000/month (12 months) | $1.15/10k emails   | High volume, cost-conscious |
| **SendGrid**   | 100/day (60 days)       | $19.95/month (10k) | Easy setup, good support    |
| **Postmark**   | 100/month (forever)     | $15/month (10k)    | Premium deliverability      |
| **EmailJS**    | 200/month (forever)     | $20/month (1k)     | Client-side only            |

## Implementation Priority

1. **Start with Amazon SES** - Best value and scaling
2. **Keep EmailJS** as immediate fallback
3. **Consider SendGrid** if SES setup is too complex

## Security Notes

- **Server-side implementation is recommended** for production
- Never expose AWS credentials in frontend code
- Use environment variables for all sensitive data
- Consider using AWS IAM roles for enhanced security
- Implement rate limiting on your email endpoint

## Next Steps

1. Set up AWS account and SES service
2. Verify your domain in SES
3. Request production access
4. Implement server-side email handling
5. Test thoroughly before deployment
6. Monitor usage and costs in AWS console

The Amazon SES approach will give you the most generous free tier (3,000 emails vs EmailJS's 200) and the best long-term pricing for scaling your business.

## Part 2: Google Maps Setup

To display the interactive map on your contact page, you need a Google Maps API key.

### 1. Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable billing (required for Maps API, but free tier is generous)

### 2. Enable Maps JavaScript API

1. Go to "APIs & Services" > "Library"
2. Search for "Maps JavaScript API"
3. Click on it and press "Enable"

### 3. Create API Key

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "API Key"
3. Copy your API key
4. (Recommended) Click "Restrict Key" and add your domain to restrictions

### 4. Configure API Key Restrictions (Optional but Recommended)

1. In the API key settings, go to "Application restrictions"
2. Select "HTTP referrers"
3. Add your domains:
   - `localhost:*` (for development)
   - `your-domain.com/*` (for production)
4. Under "API restrictions", select "Maps JavaScript API"

## Part 3: Environment Variables Setup

Create a `.env` file in your project root with all your credentials:

```env
# EmailJS Configuration
VITE_EMAILJS_PUBLIC_KEY=your_emailjs_public_key
VITE_EMAILJS_SERVICE_ID=your_emailjs_service_id
VITE_EMAILJS_TEMPLATE_ID=your_emailjs_template_id

# Google Maps Configuration
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
```

## Part 4: Update Contact Form Code

Replace the placeholder values in `src/pages/Contact.tsx`:

```typescript
// Around line 47 - EmailJS initialization
emailjs.init(import.meta.env.VITE_EMAILJS_PUBLIC_KEY);

// Around lines 85-87 - EmailJS send
await emailjs.send(
  import.meta.env.VITE_EMAILJS_SERVICE_ID,
  import.meta.env.VITE_EMAILJS_TEMPLATE_ID,
  templateParams
);
```

The Google Maps API key is already configured to use environment variables.

## Part 5: Test Everything

1. Run your development server: `pnpm dev`
2. Navigate to the contact page
3. Verify the Google Map loads correctly
4. Fill out and submit the contact form
5. Check that emails are being <NAME_EMAIL>

## Troubleshooting

### Email Issues

- Check browser console for EmailJS errors
- Verify your EmailJS credentials are correct
- Ensure your email service is properly configured
- Monitor EmailJS usage limits

### Map Issues

- Check browser console for Google Maps API errors
- Verify your API key is correct and unrestricted
- Ensure the Maps JavaScript API is enabled
- Check if you've exceeded API quotas
- Make sure billing is enabled in Google Cloud

### Common Error Messages

- `InvalidKeyMapError`: API key is invalid or restricted
- `RefererNotAllowedMapError`: Domain not allowed in API key restrictions
- `QuotaExceededError`: You've exceeded your daily quota

## Security Notes

- Environment variables in Vite are exposed to the client
- For production, consider implementing server-side email handling
- Restrict your Google Maps API key to specific domains
- Monitor usage to avoid unexpected charges
- EmailJS public keys are safe to expose in frontend code

## Pricing Information

- **EmailJS**: 200 emails/month free, then $20/month for 1000 emails
- **Google Maps**: $200 credit monthly (covers ~28,000 map loads), then $7/1000 requests
