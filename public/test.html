<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>CSS Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 20px;
      }
      .test-box {
        background-color: #4f46e5;
        color: white;
        padding: 20px;
        border-radius: 8px;
        margin: 20px 0;
      }
      .test-secondary {
        background-color: #14b8a6;
        color: white;
        padding: 15px;
        border-radius: 8px;
        margin: 10px 0;
      }
    </style>
  </head>
  <body>
    <h1>CSS Test Page</h1>
    <div class="test-box">
      <h2>Primary Color Test</h2>
      <p>
        If you can see this with a blue background (#4f46e5), basic CSS is
        working!
      </p>
    </div>
    <div class="test-secondary">
      <h3>Secondary Color Test</h3>
      <p>
        If you can see this with a teal background (#14b8a6), colors are loading
        correctly!
      </p>
    </div>
    <p><a href="/">← Back to Main Site</a></p>
  </body>
</html>
