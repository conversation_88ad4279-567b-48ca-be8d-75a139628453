import React, { Suspense } from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Layout from "./components/Layout/Layout";
import ErrorBoundary from "./components/ErrorBoundary";
import PageLoader from "./components/UI/PageLoader";

// Lazy load pages for better performance
const Home = React.lazy(() => import("./pages/Home"));
const WhatWeDo = React.lazy(() => import("./pages/WhatWeDo"));
const WhoWeAre = React.lazy(() => import("./pages/WhoWeAre"));
const Contact = React.lazy(() => import("./pages/Contact"));

export function App() {
  return (
    <ErrorBoundary>
      <Router>
        <Layout>
          <Suspense fallback={<PageLoader text="Loading page..." />}>
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/what-we-do" element={<WhatWeDo />} />
              <Route path="/who-we-are" element={<WhoWeAre />} />
              <Route path="/contact" element={<Contact />} />
            </Routes>
          </Suspense>
        </Layout>
      </Router>
    </ErrorBoundary>
  );
}
