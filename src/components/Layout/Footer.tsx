import React from "react";
import { <PERSON> } from "react-router-dom";
import { PhoneIcon, MailIcon, MapPinIcon } from "lucide-react";

const Footer: React.FC = () => {
  return (
    <footer className="bg-theme-primary text-theme-text-light w-full">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h3 className="text-lg font-semibold mb-4">
              Sanrado Techsolutions LLP
            </h3>
            <p className="opacity-90 text-sm">
              Empowering small and medium businesses with cutting-edge
              technology solutions.
            </p>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link
                  to="/"
                  className="opacity-80 hover:opacity-100 transition-opacity"
                >
                  Home
                </Link>
              </li>
              <li>
                <Link
                  to="/what-we-do"
                  className="opacity-80 hover:opacity-100 transition-opacity"
                >
                  What We Do
                </Link>
              </li>
              <li>
                <Link
                  to="/who-we-are"
                  className="opacity-80 hover:opacity-100 transition-opacity"
                >
                  Who We Are
                </Link>
              </li>
              <li>
                <Link
                  to="/contact"
                  className="opacity-80 hover:opacity-100 transition-opacity"
                >
                  Contact Us
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact</h3>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center space-x-2">
                <PhoneIcon size={16} className="opacity-80" />
                <span className="opacity-90">+91 99436 22335</span>
              </li>
              <li className="flex items-center space-x-2">
                <MailIcon size={16} className="opacity-80" />
                <span className="opacity-90"><EMAIL></span>
              </li>
              <li className="flex items-start space-x-2">
                <MapPinIcon size={16} className="opacity-80 mt-0.5" />
                <span className="opacity-90">
                  1/184, Salaithottam, Kasba Ayyampalayam
                  <br />
                  Palladam, Tiruppur, Tamil Nadu 641662, India
                </span>
              </li>
            </ul>
          </div>
        </div>
        <div className="border-t border-theme-hover-primary mt-8 pt-8 text-center text-sm opacity-70">
          <p>
            &copy; {new Date().getFullYear()} Sanrado Techsolutions LLP. All
            rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
