import React, { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { MenuIcon, XIcon } from "lucide-react";

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const navItems = [
    {
      name: "Home",
      path: "/",
    },
    {
      name: "What We Do",
      path: "/what-we-do",
    },
    {
      name: "Who We Are",
      path: "/who-we-are",
    },
    {
      name: "Contact Us",
      path: "/contact",
    },
  ];

  return (
    <header className="bg-theme-bg-primary shadow-sm w-full border-b border-theme-border">
      <div className="container mx-auto px-4 py-4 flex justify-between items-center">
        <Link to="/" className="flex items-center space-x-2">
          <div className="font-bold text-xl text-theme-secondary">
            <PERSON><PERSON> Techsolutions
          </div>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex space-x-8">
          {navItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              className={`text-sm font-medium transition-colors hover:text-theme-secondary ${
                location.pathname === item.path
                  ? "text-theme-secondary"
                  : "text-theme-text-secondary"
              }`}
            >
              {item.name}
            </Link>
          ))}
        </nav>

        {/* Mobile Menu Button */}
        <button
          className="md:hidden text-theme-text-secondary hover:text-theme-secondary"
          onClick={toggleMenu}
          aria-label="Toggle menu"
        >
          {isMenuOpen ? <XIcon size={24} /> : <MenuIcon size={24} />}
        </button>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black bg-opacity-25 z-40 md:hidden"
            onClick={() => setIsMenuOpen(false)}
            aria-hidden="true"
          />
          {/* Mobile Menu */}
          <div className="md:hidden bg-theme-bg-primary w-full shadow-lg relative z-50 border-t border-theme-border">
            <div className="container mx-auto px-4 py-4 flex flex-col space-y-4">
              {navItems.map((item) => (
                <Link
                  key={item.path}
                  to={item.path}
                  className={`text-sm font-medium transition-colors hover:text-theme-secondary py-2 px-3 rounded-md ${
                    location.pathname === item.path
                      ? "text-theme-secondary bg-theme-light"
                      : "text-theme-text-secondary hover:bg-theme-bg-alt"
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
            </div>
          </div>
        </>
      )}
    </header>
  );
};

export default Header;
