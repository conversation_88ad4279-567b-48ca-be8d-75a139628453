import React from "react";
import Header from "./Header";
import Footer from "./Footer";
import SkipNavigation from "../UI/SkipNavigation";

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className="flex flex-col min-h-screen bg-theme-bg-alt">
      <SkipNavigation />
      <Header />
      <main id="main-content" className="flex-grow" tabIndex={-1}>
        {children}
      </main>
      <Footer />
    </div>
  );
};

export default Layout;
