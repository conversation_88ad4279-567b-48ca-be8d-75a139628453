import React from "react";
import { env } from "../../utils/env";

interface PageHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  ogImage?: string;
  canonical?: string;
}

const PageHead: React.FC<PageHeadProps> = ({
  title,
  description,
  keywords,
  ogImage,
  canonical,
}) => {
  const pageTitle = title
    ? `${title} | ${env.SITE_NAME}`
    : `${env.SITE_NAME} - Technology Solutions for Growing Businesses`;
  const pageDescription =
    description ||
    `Empowering small and medium businesses with cutting-edge technology solutions. Cloud services, cybersecurity, mobile apps, and IT support.`;
  const pageKeywords =
    keywords ||
    "technology solutions, small business, medium business, cloud services, cybersecurity, mobile apps, web development, IT support";
  const pageOgImage = ogImage || `${env.SITE_URL}/og-image.jpg`;
  const pageCanonical = canonical || env.SITE_URL;

  React.useEffect(() => {
    // Update document title
    document.title = pageTitle;

    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute("content", pageDescription);
    }

    // Update meta keywords
    const metaKeywords = document.querySelector('meta[name="keywords"]');
    if (metaKeywords) {
      metaKeywords.setAttribute("content", pageKeywords);
    }

    // Update Open Graph tags
    const ogTitle = document.querySelector('meta[property="og:title"]');
    if (ogTitle) {
      ogTitle.setAttribute("content", pageTitle);
    }

    const ogDesc = document.querySelector('meta[property="og:description"]');
    if (ogDesc) {
      ogDesc.setAttribute("content", pageDescription);
    }

    const ogImageMeta = document.querySelector('meta[property="og:image"]');
    if (ogImageMeta) {
      ogImageMeta.setAttribute("content", pageOgImage);
    }

    const ogUrl = document.querySelector('meta[property="og:url"]');
    if (ogUrl) {
      ogUrl.setAttribute("content", pageCanonical);
    }

    // Update Twitter tags
    const twitterTitle = document.querySelector(
      'meta[property="twitter:title"]'
    );
    if (twitterTitle) {
      twitterTitle.setAttribute("content", pageTitle);
    }

    const twitterDesc = document.querySelector(
      'meta[property="twitter:description"]'
    );
    if (twitterDesc) {
      twitterDesc.setAttribute("content", pageDescription);
    }

    const twitterImage = document.querySelector(
      'meta[property="twitter:image"]'
    );
    if (twitterImage) {
      twitterImage.setAttribute("content", pageOgImage);
    }

    const twitterUrl = document.querySelector('meta[property="twitter:url"]');
    if (twitterUrl) {
      twitterUrl.setAttribute("content", pageCanonical);
    }

    // Update canonical link
    let canonicalLink = document.querySelector('link[rel="canonical"]');
    if (!canonicalLink) {
      canonicalLink = document.createElement("link");
      canonicalLink.setAttribute("rel", "canonical");
      document.head.appendChild(canonicalLink);
    }
    canonicalLink.setAttribute("href", pageCanonical);
  }, [pageTitle, pageDescription, pageKeywords, pageOgImage, pageCanonical]);

  return null; // This component doesn't render anything
};

export default PageHead;
