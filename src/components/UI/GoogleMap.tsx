import React, { useRef, useEffect, useState } from "react";

interface MapProps {
  center: { lat: number; lng: number };
  zoom: number;
  height?: string;
}

const MapComponent: React.FC<MapProps> = ({
  center,
  zoom,
  height = "400px",
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<google.maps.Map>();

  useEffect(() => {
    if (ref.current && !map) {
      const newMap = new window.google.maps.Map(ref.current, {
        center,
        zoom,
        mapTypeControl: true,
        streetViewControl: true,
        fullscreenControl: true,
        zoomControl: true,
        styles: [
          {
            featureType: "poi.business",
            elementType: "labels",
            stylers: [{ visibility: "off" }],
          },
        ],
      });

      // Add marker
      const marker = new google.maps.Marker({
        position: center,
        map: newMap,
        title: "Sanrado Techsolutions LLP",
        animation: google.maps.Animation.DROP,
      });

      // Add info window
      const infoWindow = new google.maps.InfoWindow({
        content: `
          <div style="padding: 10px;">
            <h3 style="margin: 0 0 5px 0; color: #1f2937;">Sanrado Techsolutions LLP</h3>
            <p style="margin: 0; color: #6b7280; font-size: 14px;">
              1/184, Salaithottam, Kasba Ayyampalayam<br>
              Palladam, Tiruppur, Tamil Nadu 641662
            </p>
          </div>
        `,
      });

      marker.addListener("click", () => {
        infoWindow.open(newMap, marker);
      });

      setMap(newMap);
    }
  }, [ref, map, center, zoom]);

  // Check if Google Maps is loaded
  if (typeof window === "undefined" || !window.google) {
    return (
      <div
        className="flex items-center justify-center bg-theme-bg-alt rounded-lg border border-theme-border"
        style={{ height }}
      >
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-theme-secondary mx-auto mb-2"></div>
          <p className="text-theme-text-secondary">Loading map...</p>
        </div>
      </div>
    );
  }

  return <div ref={ref} style={{ height }} className="w-full rounded-lg" />;
};

// Google Maps API loader component
const GoogleMap: React.FC<MapProps> = (props) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [loadError, setLoadError] = useState(false);

  useEffect(() => {
    if (window.google && window.google.maps) {
      setIsLoaded(true);
      return;
    }

    const script = document.createElement("script");
    script.src = `https://maps.googleapis.com/maps/api/js?key=${
      import.meta.env.VITE_GOOGLE_MAPS_API_KEY || ""
    }&libraries=places`;
    script.async = true;
    script.defer = true;

    script.onload = () => setIsLoaded(true);
    script.onerror = () => setLoadError(true);

    document.head.appendChild(script);

    return () => {
      document.head.removeChild(script);
    };
  }, []);

  if (loadError) {
    return (
      <div
        className="flex items-center justify-center bg-theme-bg-alt rounded-lg border border-theme-border"
        style={{ height: props.height }}
      >
        <p className="text-theme-text-secondary">Unable to load map</p>
      </div>
    );
  }

  if (!isLoaded) {
    return (
      <div
        className="flex items-center justify-center bg-theme-bg-alt rounded-lg border border-theme-border"
        style={{ height: props.height }}
      >
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-theme-secondary mx-auto mb-2"></div>
          <p className="text-theme-text-secondary">Loading map...</p>
        </div>
      </div>
    );
  }

  return <MapComponent {...props} />;
};

export default GoogleMap;
