@import "tailwindcss";

/* Theme System - Indigo & Teal (Refined Current) */
:root {
  --color-primary: #4f46e5; /* Indigo 600 */
  --color-secondary: #14b8a6; /* Teal 500 */
  --color-accent: #0891b2; /* <PERSON>an 600 */
  --color-light: #eef2ff; /* Indigo 50 */
  --color-text-primary: #312e81; /* Indigo 800 */
  --color-text-secondary: #1f2937; /* Gray 800 */
  --color-text-light: #ffffff;
  --color-background: #ffffff;
  --color-background-alt: #f0f9ff; /* Sky 50 */
  --color-border: #c7d2fe; /* Indigo 200 */
  --color-hover-primary: #3730a3; /* Darker primary */
  --color-hover-secondary: #0f766e; /* Darker secondary */
}

/* Theme System - Change this import to switch themes easily */
/* Available themes:
   - "./themes/theme-tech-professional.css" (default)
   - "./themes/theme-modern-corporate.css"
   - "./themes/theme-bold-professional.css"
   - "./themes/theme-premium-trust.css"
   - "./themes/theme-creative-tech.css"
   - "./themes/theme-refined-current.css"
*/
@import "./themes/theme-refined-current.css";

/* Reset and apply fonts globally */
html {
  font-family: "Atkinson Hyperlegible", system-ui, sans-serif !important;
}

h1,
h2,
h3,
h4,
h5,
h6,
.font-heading {
  font-family: "Lato", system-ui, sans-serif !important;
}

/* Apply theme colors to base elements */
body {
  background-color: #ffffff !important;
  color: #312e81 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Theme colors will be handled by Tailwind CSS custom color system */

/* Debugging styles */
.debug-theme {
  background-color: #4f46e5 !important;
  color: #ffffff !important;
  padding: 1rem !important;
  margin: 1rem !important;
  border-radius: 0.5rem !important;
  font-weight: bold !important;
  border: 2px solid #14b8a6 !important;
}

/* Additional component fixes */
.container {
  max-width: 1200px !important;
  margin: 0 auto !important;
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

/* Button styles fallback */
.btn-primary {
  background-color: #4f46e5 !important;
  color: #ffffff !important;
  padding: 0.75rem 1.5rem !important;
  border-radius: 0.5rem !important;
  border: none !important;
  text-decoration: none !important;
  display: inline-block !important;
}

.btn-primary:hover {
  background-color: #3730a3 !important;
}

/* Navigation styles */
nav {
  background-color: #ffffff !important;
  border-bottom: 1px solid #c7d2fe !important;
}

/* Hero section specific styles */
section {
  width: 100% !important;
}
