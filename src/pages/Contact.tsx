import React, { useState } from "react";
import PageHead from "../components/SEO/PageHead";
import GoogleMap from "../components/UI/GoogleMap";
import {
  PhoneIcon,
  MailIcon,
  MapPinIcon,
  ClockIcon,
  FacebookIcon,
  TwitterIcon,
  LinkedinIcon,
  InstagramIcon,
  CheckCircleIcon,
} from "lucide-react";

interface FormState {
  name: string;
  email: string;
  company: string;
  phone: string;
  service: string;
  budget: string;
  message: string;
  submitted: boolean;
  isSubmitting: boolean;
}

interface FormErrors {
  name?: string;
  email?: string;
  message?: string;
}

const faqData = [
  {
    question: "What is your typical project timeline?",
    answer:
      "Project timelines vary depending on scope and complexity. Simple websites typically take 2-4 weeks, while complex web applications can take 2-6 months. We'll provide a detailed timeline during our initial consultation.",
  },
  {
    question: "Do you provide ongoing support and maintenance?",
    answer:
      "Yes, we offer comprehensive support and maintenance packages to ensure your technology solutions continue to perform optimally. Our support includes updates, security patches, monitoring, and technical assistance.",
  },
  {
    question: "Can you work with our existing systems?",
    answer:
      "Absolutely! We specialize in integrating new solutions with existing systems and can help modernize your current infrastructure while maintaining business continuity.",
  },
  {
    question: "What industries do you primarily serve?",
    answer:
      "We work across various industries including FinTech, Healthcare, Real Estate, Web3, SaaS, and eCommerce. Our solutions are designed to meet the unique challenges of each industry while maintaining flexibility for diverse business needs.",
  },
];

const Contact: React.FC = () => {
  const [formState, setFormState] = useState<FormState>({
    name: "",
    email: "",
    company: "",
    phone: "",
    service: "",
    budget: "",
    message: "",
    submitted: false,
    isSubmitting: false,
  });

  const [errors, setErrors] = useState<FormErrors>({});

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formState.name.trim()) {
      newErrors.name = "Name is required";
    }

    if (!formState.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formState.email)) {
      newErrors.email = "Email is invalid";
    }

    if (!formState.message.trim()) {
      newErrors.message = "Message is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setFormState((prev) => ({ ...prev, isSubmitting: true }));

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000));

    setFormState((prev) => ({
      ...prev,
      submitted: true,
      isSubmitting: false,
    }));
  };

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormState((prev) => ({ ...prev, [name]: value }));

    // Clear error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  const mapCenter = { lat: 11.0168, lng: 76.9558 }; // Approximate coordinates for the address

  return (
    <>
      <PageHead
        title="Contact Us"
        description="Get in touch with Sanrado Techsolutions LLP. We're here to help transform your business with innovative technology solutions."
        keywords="contact, consultation, technology solutions, business transformation, Sanrado"
      />
      <div className="w-full">
        {/* Hero Section */}
        <section className="bg-theme-bg-alt py-20">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center">
              <h1 className="text-4xl font-bold mb-6 text-theme-text-primary">
                Contact Us
              </h1>
              <p className="text-xl text-theme-text-secondary">
                Ready to transform your business with innovative technology
                solutions? Let's start the conversation.
              </p>
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section className="py-16 bg-theme-bg-primary">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
              {/* Contact Information */}
              <div className="lg:col-span-1">
                <h2 className="text-2xl font-bold mb-6 text-theme-text-primary">
                  Get in Touch
                </h2>
                <p className="text-theme-text-secondary mb-8">
                  We'd love to hear about your project and discuss how we can
                  help you achieve your technology goals.
                </p>

                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <PhoneIcon
                      size={20}
                      className="text-theme-secondary mt-1"
                    />
                    <div>
                      <p className="text-theme-text-secondary">
                        +91 99436 22335
                      </p>
                      <p className="text-sm text-theme-text-secondary opacity-75">
                        Mon-Fri, 9AM-6PM IST
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <MailIcon size={20} className="text-theme-secondary mt-1" />
                    <div>
                      <p className="text-theme-text-secondary">
                        <EMAIL>
                      </p>
                      <p className="text-sm text-theme-text-secondary opacity-75">
                        We'll respond within 24 hours
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <MapPinIcon
                      size={20}
                      className="text-theme-secondary mt-1"
                    />
                    <div>
                      <p className="text-theme-text-secondary">
                        1/184, Salaithottam, Kasba Ayyampalayam,
                        <br />
                        Palladam, Tiruppur, Tamil Nadu 641662, India
                      </p>
                    </div>
                  </div>
                </div>

                {/* Business Hours */}
                <div className="bg-theme-bg-alt p-6 rounded-lg mb-8 mt-8">
                  <div className="flex items-center mb-2">
                    <ClockIcon
                      size={18}
                      className="mr-2 text-theme-secondary"
                    />
                    <h3 className="font-semibold text-theme-text-primary">
                      Business Hours
                    </h3>
                  </div>
                  <div className="text-sm text-theme-text-secondary space-y-1">
                    <div className="flex justify-between">
                      <span>Monday - Friday</span>
                      <span>9:00 AM - 6:00 PM</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Saturday</span>
                      <span>10:00 AM - 4:00 PM</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Sunday</span>
                      <span>Closed</span>
                    </div>
                  </div>
                </div>

                {/* Social Links */}
                <div className="flex space-x-4">
                  <a
                    href="#"
                    className="text-theme-text-secondary hover:text-theme-secondary transition-colors"
                    aria-label="Facebook"
                  >
                    <FacebookIcon size={20} />
                  </a>
                  <a
                    href="#"
                    className="text-theme-text-secondary hover:text-theme-secondary transition-colors"
                    aria-label="Twitter"
                  >
                    <TwitterIcon size={20} />
                  </a>
                  <a
                    href="#"
                    className="text-theme-text-secondary hover:text-theme-secondary transition-colors"
                    aria-label="LinkedIn"
                  >
                    <LinkedinIcon size={20} />
                  </a>
                  <a
                    href="#"
                    className="text-theme-text-secondary hover:text-theme-secondary transition-colors"
                    aria-label="Instagram"
                  >
                    <InstagramIcon size={20} />
                  </a>
                </div>
              </div>

              {/* Contact Form */}
              <div className="lg:col-span-2">
                <h2 className="text-2xl font-bold mb-6 text-theme-text-primary">
                  Send Us a Message
                </h2>

                {formState.submitted ? (
                  <div className="bg-theme-light border border-theme-secondary rounded-lg p-8 text-center">
                    <CheckCircleIcon
                      size={48}
                      className="text-theme-secondary mx-auto mb-4"
                    />
                    <h3 className="text-xl font-bold text-theme-text-primary mb-2">
                      Message Sent Successfully!
                    </h3>
                    <p className="text-theme-text-secondary">
                      Thank you for reaching out. We'll get back to you within
                      24 hours at {formState.email}.
                    </p>
                  </div>
                ) : (
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label
                          htmlFor="name"
                          className="block text-sm font-medium text-theme-text-primary mb-1"
                        >
                          Full Name *
                        </label>
                        <input
                          type="text"
                          id="name"
                          name="name"
                          value={formState.name}
                          onChange={handleInputChange}
                          className={`w-full px-4 py-2 border rounded-md focus:ring-theme-secondary focus:border-theme-secondary ${
                            errors.name
                              ? "border-red-500"
                              : "border-theme-border"
                          }`}
                          placeholder="Your full name"
                        />
                        {errors.name && (
                          <p className="text-red-500 text-xs mt-1">
                            {errors.name}
                          </p>
                        )}
                      </div>

                      <div>
                        <label
                          htmlFor="email"
                          className="block text-sm font-medium text-theme-text-primary mb-1"
                        >
                          Email Address *
                        </label>
                        <input
                          type="email"
                          id="email"
                          name="email"
                          value={formState.email}
                          onChange={handleInputChange}
                          className={`w-full px-4 py-2 border rounded-md focus:ring-theme-secondary focus:border-theme-secondary ${
                            errors.email
                              ? "border-red-500"
                              : "border-theme-border"
                          }`}
                          placeholder="<EMAIL>"
                        />
                        {errors.email && (
                          <p className="text-red-500 text-xs mt-1">
                            {errors.email}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label
                          htmlFor="company"
                          className="block text-sm font-medium text-theme-text-primary mb-1"
                        >
                          Company/Organization
                        </label>
                        <input
                          type="text"
                          id="company"
                          name="company"
                          value={formState.company}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 border border-theme-border rounded-md focus:ring-theme-secondary focus:border-theme-secondary"
                          placeholder="Your company name"
                        />
                      </div>

                      <div>
                        <label
                          htmlFor="phone"
                          className="block text-sm font-medium text-theme-text-primary mb-1"
                        >
                          Phone Number
                        </label>
                        <input
                          type="tel"
                          id="phone"
                          name="phone"
                          value={formState.phone}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 border border-theme-border rounded-md focus:ring-theme-secondary focus:border-theme-secondary"
                          placeholder="+****************"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label
                          htmlFor="service"
                          className="block text-sm font-medium text-theme-text-primary mb-1"
                        >
                          Service of Interest
                        </label>
                        <select
                          id="service"
                          name="service"
                          value={formState.service}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 border border-theme-border rounded-md focus:ring-theme-secondary focus:border-theme-secondary"
                        >
                          <option value="">Select a service</option>
                          <option value="web-development">
                            Web Development
                          </option>
                          <option value="mobile-development">
                            Mobile App Development
                          </option>
                          <option value="cloud-solutions">
                            Cloud Solutions
                          </option>
                          <option value="cybersecurity">Cybersecurity</option>
                          <option value="data-analytics">Data Analytics</option>
                          <option value="it-support">IT Support</option>
                          <option value="consultation">
                            Technology Consultation
                          </option>
                          <option value="other">Other</option>
                        </select>
                      </div>

                      <div>
                        <label
                          htmlFor="budget"
                          className="block text-sm font-medium text-theme-text-primary mb-1"
                        >
                          Project Budget Range
                        </label>
                        <select
                          id="budget"
                          name="budget"
                          value={formState.budget}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 border border-theme-border rounded-md focus:ring-theme-secondary focus:border-theme-secondary"
                        >
                          <option value="">Select budget range</option>
                          <option value="under-10k">Under $10,000</option>
                          <option value="10k-25k">$10,000 - $25,000</option>
                          <option value="25k-50k">$25,000 - $50,000</option>
                          <option value="50k-100k">$50,000 - $100,000</option>
                          <option value="over-100k">Over $100,000</option>
                          <option value="discuss">Prefer to discuss</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <label
                        htmlFor="message"
                        className="block text-sm font-medium text-theme-text-primary mb-1"
                      >
                        Project Details *
                      </label>
                      <textarea
                        id="message"
                        name="message"
                        rows={6}
                        value={formState.message}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-2 border rounded-md focus:ring-theme-secondary focus:border-theme-secondary ${
                          errors.message
                            ? "border-red-500"
                            : "border-theme-border"
                        }`}
                        placeholder="Tell us about your project, goals, timeline, and any specific requirements..."
                      />
                      {errors.message && (
                        <p className="text-red-500 text-xs mt-1">
                          {errors.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <button
                        type="submit"
                        disabled={formState.isSubmitting}
                        className="w-full bg-theme-secondary text-theme-text-light py-3 px-6 rounded-md hover:bg-theme-hover-secondary transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                      >
                        {formState.isSubmitting ? (
                          <>
                            <svg
                              className="animate-spin -ml-1 mr-3 h-5 w-5"
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                            >
                              <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"
                              />
                              <path
                                className="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                              />
                            </svg>
                            Sending Message...
                          </>
                        ) : (
                          "Send Message"
                        )}
                      </button>
                    </div>
                  </form>
                )}
              </div>
            </div>
          </div>
        </section>

        {/* Map Section */}
        <section className="py-16 bg-theme-bg-alt">
          <div className="container mx-auto px-4">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold mb-4 text-theme-text-primary">
                Visit Our Office
              </h2>
              <p className="text-theme-text-secondary max-w-2xl mx-auto">
                Located in the heart of Tamil Nadu's textile hub, our office is
                easily accessible and we welcome visitors by appointment.
              </p>
            </div>
            <div className="max-w-4xl mx-auto">
              <GoogleMap center={mapCenter} zoom={15} height="400px" />
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-16 bg-theme-bg-primary">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold mb-4 text-theme-text-primary">
                  Frequently Asked Questions
                </h2>
                <p className="text-theme-text-secondary">
                  Here are some common questions about our services and process.
                </p>
              </div>

              <div className="space-y-6">
                {faqData.map((faq, index) => (
                  <details key={index} className="bg-theme-bg-alt rounded-lg">
                    <summary className="cursor-pointer p-6 font-medium text-theme-text-primary hover:text-theme-secondary">
                      {faq.question}
                    </summary>
                    <div className="px-6 pb-6 text-theme-text-secondary">
                      {faq.answer}
                    </div>
                  </details>
                ))}
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default Contact;
