import React from "react";
import Button from "../components/UI/Button";
import PageHead from "../components/SEO/PageHead";
import {
  ArrowRightIcon,
  BarChartIcon,
  LayersIcon,
  ZapIcon,
} from "lucide-react";

const Home: React.FC = () => {
  return (
    <>
      <PageHead
        title="Home"
        description="Empowering small and medium businesses with cutting-edge technology solutions. Cloud services, cybersecurity, mobile apps, web development, and IT support."
        keywords="technology solutions, small business, medium business, cloud services, cybersecurity, mobile apps, web development, IT support, Sanrado"
      />
      <div className="w-full">
        {/* Hero Section */}
        <section className="bg-theme-primary text-theme-text-light py-20 md:py-32">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center">
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                Empowering Small & Medium
                <br />
                <span className="text-theme-secondary">
                  Businesses with Technology
                </span>
              </h1>
              <p className="text-xl mb-8 opacity-90">
                We specialize in building web, mobile, and cloud applications
                for FinTech, Healthcare, Real Estate, Web3, SaaS, and eCommerce
                businesses.
              </p>
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <Button variant="outline" size="lg" href="/what-we-do">
                  Explore Solutions
                </Button>
                <Button variant="primary" size="lg" href="/contact">
                  Get Started
                </Button>
              </div>
            </div>
          </div>
        </section>
        {/* Features Section */}
        <section className="py-16 md:py-24 bg-theme-bg-primary">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold mb-4 text-theme-text-primary">
                How We Help Businesses
              </h2>
              <p className="text-theme-text-secondary max-w-2xl mx-auto">
                Our technology solutions are designed specifically for the
                unique challenges faced by small and medium businesses.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-theme-bg-alt p-8 rounded-lg text-center">
                <div className="bg-theme-light rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <ZapIcon className="text-theme-secondary" size={24} />
                </div>
                <h3 className="text-xl font-bold mb-2 text-theme-text-primary">
                  Boost Productivity
                </h3>
                <p className="text-theme-text-secondary">
                  Streamline operations and automate repetitive tasks to free up
                  your team's time for what matters most.
                </p>
              </div>
              <div className="bg-theme-bg-alt p-8 rounded-lg text-center">
                <div className="bg-theme-light rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <BarChartIcon className="text-theme-secondary" size={24} />
                </div>
                <h3 className="text-xl font-bold mb-2 text-theme-text-primary">
                  Drive Growth
                </h3>
                <p className="text-theme-text-secondary">
                  Leverage data-driven insights and digital tools to reach new
                  customers and expand your business.
                </p>
              </div>
              <div className="bg-theme-bg-alt p-8 rounded-lg text-center">
                <div className="bg-theme-light rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <LayersIcon className="text-theme-secondary" size={24} />
                </div>
                <h3 className="text-xl font-bold mb-2 text-theme-text-primary">
                  Scale Efficiently
                </h3>
                <p className="text-theme-text-secondary">
                  Build flexible technology infrastructure that grows with your
                  business without breaking the bank.
                </p>
              </div>
            </div>
          </div>
        </section>
        {/* Stats Section */}
        <section className="py-16 bg-theme-secondary text-theme-text-light">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-4xl font-bold mb-2">150+</div>
                <p className="opacity-90">Projects Delivered</p>
              </div>
              <div>
                <div className="text-4xl font-bold mb-2">50+</div>
                <p className="opacity-90">Happy Clients</p>
              </div>
              <div>
                <div className="text-4xl font-bold mb-2">6</div>
                <p className="opacity-90">Industry Specializations</p>
              </div>
              <div>
                <div className="text-4xl font-bold mb-2">99%</div>
                <p className="opacity-90">Client Satisfaction</p>
              </div>
            </div>
          </div>
        </section>
        {/* CTA Section */}
        <section className="bg-theme-bg-alt py-16">
          <div className="container mx-auto px-4 text-center">
            <div className="max-w-3xl mx-auto">
              <h2 className="text-3xl font-bold mb-4 text-theme-text-primary">
                Ready to Transform Your Business?
              </h2>
              <p className="text-theme-text-secondary mb-8">
                Let's discuss how our technology solutions can help your
                business grow and succeed in today's digital landscape.
              </p>
              <Button variant="primary" size="lg" href="/contact">
                Get Started Today
                <ArrowRightIcon className="ml-2" size={20} />
              </Button>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default Home;
