import React from "react";
import Button from "../components/UI/Button";
import Card from "../components/UI/Card";
import PageHead from "../components/SEO/PageHead";
import {
  CloudIcon,
  DatabaseIcon,
  HeadphonesIcon,
  MonitorIcon,
  ShieldIcon,
  SmartphoneIcon,
} from "lucide-react";

const WhatWeDo: React.FC = () => {
  const services = [
    {
      title: "Web Development",
      description:
        "Custom web applications built with modern frameworks and technologies to drive your business forward.",
      icon: <MonitorIcon size={32} className="text-theme-secondary" />,
    },
    {
      title: "Mobile App Development",
      description:
        "Native and cross-platform mobile applications that engage your customers and expand your reach.",
      icon: <SmartphoneIcon size={32} className="text-theme-secondary" />,
    },
    {
      title: "Cloud Solutions",
      description:
        "Scalable cloud infrastructure and migration services to optimize your operations and reduce costs.",
      icon: <CloudIcon size={32} className="text-theme-secondary" />,
    },
    {
      title: "Cybersecurity",
      description:
        "Comprehensive security solutions to protect your business data and maintain customer trust.",
      icon: <ShieldIcon size={32} className="text-theme-secondary" />,
    },
    {
      title: "Data Analytics",
      description:
        "Turn your data into actionable insights with our advanced analytics and business intelligence solutions.",
      icon: <DatabaseIcon size={32} className="text-theme-secondary" />,
    },
    {
      title: "IT Support",
      description:
        "24/7 technical support and maintenance to keep your systems running smoothly and efficiently.",
      icon: <HeadphonesIcon size={32} className="text-theme-secondary" />,
    },
  ];

  const industries = [
    {
      title: "FinTech",
      description:
        "Revolutionary financial technology solutions for modern banking, payments, and investment platforms.",
      highlights: [
        "Payment processing systems",
        "Digital banking platforms",
        "Investment management tools",
        "Regulatory compliance solutions",
      ],
    },
    {
      title: "Healthcare",
      description:
        "Digital health solutions that improve patient care and streamline healthcare operations.",
      highlights: [
        "Electronic health records",
        "Telemedicine platforms",
        "Patient management systems",
        "HIPAA compliant solutions",
      ],
    },
    {
      title: "Real Estate",
      description:
        "Technology solutions that modernize property management and real estate transactions.",
      highlights: [
        "Property management platforms",
        "CRM and lead tracking",
        "Virtual property tours",
        "Transaction management",
      ],
    },
    {
      title: "Web3 & Blockchain",
      description:
        "Cutting-edge blockchain solutions and decentralized applications for the future of business.",
      highlights: [
        "Smart contract development",
        "DeFi applications",
        "NFT marketplaces",
        "Cryptocurrency solutions",
      ],
    },
    {
      title: "SaaS Platforms",
      description:
        "Scalable software-as-a-service solutions that grow with your business needs.",
      highlights: [
        "Multi-tenant architecture",
        "Subscription management",
        "API integrations",
        "Analytics dashboards",
      ],
    },
    {
      title: "eCommerce",
      description:
        "Comprehensive e-commerce solutions that drive sales and enhance customer experience.",
      highlights: [
        "Custom online stores",
        "Payment gateway integration",
        "Inventory management",
        "Mobile commerce apps",
      ],
    },
  ];

  return (
    <>
      <PageHead
        title="What We Do"
        description="Discover our comprehensive technology services including web development, mobile apps, cloud solutions, cybersecurity, and specialized industry expertise."
        keywords="web development, mobile apps, cloud solutions, cybersecurity, fintech, healthcare, real estate, web3, saas, ecommerce"
      />
      <div className="w-full">
        {/* Hero Section */}
        <section className="bg-theme-bg-alt py-20">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center">
              <h1 className="text-4xl font-bold mb-6 text-theme-text-primary">
                What We Do
              </h1>
              <p className="text-xl text-theme-text-secondary mb-8">
                We provide comprehensive technology solutions designed to
                transform how small and medium businesses operate, compete, and
                grow in the digital economy.
              </p>
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 bg-theme-bg-primary">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold mb-4 text-theme-text-primary">
                Our Services
              </h2>
              <p className="text-theme-text-secondary max-w-2xl mx-auto">
                From web development to cloud solutions, we offer a full
                spectrum of technology services to meet your business needs.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {services.map((service, index) => (
                <Card key={index} className="flex flex-col h-full text-center">
                  <div className="mb-4">{service.icon}</div>
                  <h3 className="text-xl font-bold mb-2 text-theme-text-primary">
                    {service.title}
                  </h3>
                  <p className="text-theme-text-secondary mb-4 flex-grow">
                    {service.description}
                  </p>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Our Approach Section */}
        <section className="py-16 bg-theme-bg-alt">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold mb-4 text-theme-text-primary">
                  Our Approach
                </h2>
                <p className="text-theme-text-secondary">
                  We believe in creating technology solutions that are tailored
                  to your specific business needs.
                </p>
              </div>
              <div className="space-y-8">
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="bg-theme-light rounded-full w-12 h-12 flex items-center justify-center shrink-0">
                    <span className="text-theme-secondary font-bold">1</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold mb-2 text-theme-text-primary">
                      Understand
                    </h3>
                    <p className="text-theme-text-secondary">
                      We start by deeply understanding your business goals,
                      challenges, and current technology landscape.
                    </p>
                  </div>
                </div>
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="bg-theme-light rounded-full w-12 h-12 flex items-center justify-center shrink-0">
                    <span className="text-theme-secondary font-bold">2</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold mb-2 text-theme-text-primary">
                      Plan
                    </h3>
                    <p className="text-theme-text-secondary">
                      We develop a strategic technology roadmap aligned with
                      your business objectives and budget.
                    </p>
                  </div>
                </div>
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="bg-theme-light rounded-full w-12 h-12 flex items-center justify-center shrink-0">
                    <span className="text-theme-secondary font-bold">3</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold mb-2 text-theme-text-primary">
                      Implement
                    </h3>
                    <p className="text-theme-text-secondary">
                      We deploy solutions with minimal disruption to your
                      business operations, ensuring a smooth transition.
                    </p>
                  </div>
                </div>
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="bg-theme-light rounded-full w-12 h-12 flex items-center justify-center shrink-0">
                    <span className="text-theme-secondary font-bold">4</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold mb-2 text-theme-text-primary">
                      Support
                    </h3>
                    <p className="text-theme-text-secondary">
                      We provide ongoing support and optimization to ensure your
                      technology continues to serve your evolving business
                      needs.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        {/* Industry Specializations Section */}
        <section className="py-16 bg-theme-bg-primary">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold mb-4 text-theme-text-primary">
                Industry Specializations
              </h2>
              <p className="text-theme-text-secondary max-w-3xl mx-auto">
                We bring deep industry knowledge and specialized expertise to
                deliver solutions that understand your unique business
                challenges and regulatory requirements.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {industries.map((industry, index) => (
                <Card key={index} className="flex flex-col h-full">
                  <h3 className="text-xl font-bold mb-3 text-theme-secondary">
                    {industry.title}
                  </h3>
                  <p className="text-theme-text-secondary mb-4 flex-grow">
                    {industry.description}
                  </p>
                  <div className="space-y-2">
                    {industry.highlights.map((highlight, idx) => (
                      <div
                        key={idx}
                        className="flex items-center text-sm text-theme-text-primary"
                      >
                        <div className="w-2 h-2 bg-theme-secondary rounded-full mr-3"></div>
                        {highlight}
                      </div>
                    ))}
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </section>
        {/* CTA Section */}
        <section className="py-16 bg-theme-secondary text-theme-text-light">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-3xl font-bold mb-4">Ready to get started?</h2>
              <p className="mb-8">
                Contact us today to schedule a free consultation and learn how
                our services can benefit your business.
              </p>
              <Button
                variant="primary"
                size="lg"
                href="/contact"
                className="bg-theme-text-light text-theme-secondary hover:bg-theme-bg-alt"
              >
                Contact Us
              </Button>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default WhatWeDo;
