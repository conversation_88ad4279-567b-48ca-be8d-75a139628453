# Theme System Documentation

## Overview

This theme system allows you to easily switch between 6 different color palettes for your website. Each theme is designed for different business personalities and use cases.

## Available Themes

### 1. Tech Professional (Default)

- **File**: `theme-tech-professional.css`
- **Colors**: Deep Navy & Electric Blue
- **Best for**: IT services, technology companies
- **Primary**: #1e293b (Slate 800)
- **Secondary**: #3b82f6 (Blue 500)

### 2. Modern Corporate

- **File**: `theme-modern-corporate.css`
- **Colors**: Charcoal & Emerald
- **Best for**: Corporate businesses, consulting
- **Primary**: #374151 (Gray 700)
- **Secondary**: #10b981 (Emerald 500)

### 3. Bold Professional

- **File**: `theme-bold-professional.css`
- **Colors**: Midnight Blue & Orange
- **Best for**: Dynamic businesses, agencies
- **Primary**: #1e40af (Blue 800)
- **Secondary**: #f97316 (Orange 500)

### 4. Premium Trust

- **File**: `theme-premium-trust.css`
- **Colors**: Forest Green & Gold
- **Best for**: Financial services, luxury brands
- **Primary**: #166534 (Green 800)
- **Secondary**: #facc15 (Yellow 400)

### 5. Creative Tech

- **File**: `theme-creative-tech.css`
- **Colors**: Purple & Coral
- **Best for**: Startups, creative agencies
- **Primary**: #7c3aed (Violet 600)
- **Secondary**: #fb7185 (Rose 400)

### 6. Refined Current

- **File**: `theme-refined-current.css`
- **Colors**: Indigo & Teal
- **Best for**: Professional services, maintains current teal
- **Primary**: #4f46e5 (Indigo 600)
- **Secondary**: #14b8a6 (Teal 500)

## How to Switch Themes

1. Open `src/index.css`
2. Change the theme import line:
   ```css
   @import "./themes/theme-tech-professional.css";
   ```
   To any of the available themes:
   ```css
   @import "./themes/theme-modern-corporate.css";
   ```

## Using Theme Colors in Components

### New Theme Classes (Recommended)

Use the new theme classes that automatically adapt to the current theme:

```jsx
// Primary colors
<div className="bg-theme-primary text-theme-text-light">
<button className="bg-theme-secondary hover:bg-theme-hover-secondary">

// Background colors
<section className="bg-theme-bg-alt">
<div className="bg-theme-bg-primary border border-theme-border">

// Text colors
<h1 className="text-theme-text-primary">
<p className="text-theme-text-secondary">
```

### CSS Variables (For Custom Styles)

You can also use the CSS variables directly:

```css
.custom-element {
  background-color: var(--color-primary);
  color: var(--color-text-light);
  border: 1px solid var(--color-border);
}
```

## Migration Guide

To migrate existing components from hardcoded colors to the theme system:

### Before (Hardcoded)

```jsx
<div className="bg-blue-600 text-white hover:bg-blue-700">
  <h2 className="text-blue-600">Title</h2>
  <p className="text-gray-600">Description</p>
</div>
```

### After (Theme-based)

```jsx
<div className="bg-theme-primary text-theme-text-light hover:bg-theme-hover-primary">
  <h2 className="text-theme-secondary">Title</h2>
  <p className="text-theme-text-secondary">Description</p>
</div>
```

## Available CSS Variables

### Colors

- `--color-primary`: Main brand color
- `--color-secondary`: Secondary brand color
- `--color-accent`: Accent color for highlights
- `--color-light`: Light background/surface color

### Text Colors

- `--color-text-primary`: Primary text color
- `--color-text-secondary`: Secondary/muted text color
- `--color-text-light`: Light text color (for dark backgrounds)

### Background Colors

- `--color-background`: Primary background color
- `--color-background-alt`: Alternative background color

### Interactive Colors

- `--color-hover-primary`: Hover state for primary elements
- `--color-hover-secondary`: Hover state for secondary elements
- `--color-border`: Border color

### Utility Variables

- `--transition-default`: Default transition timing
- `--shadow-default`: Default shadow
- `--shadow-hover`: Hover shadow
- `--border-radius-default`: Default border radius
- `--border-radius-large`: Large border radius

## Best Practices

1. **Always use theme classes** when possible instead of hardcoded Tailwind colors
2. **Test all themes** before deployment to ensure consistent appearance
3. **Use semantic naming** - prefer `bg-theme-primary` over `bg-blue-600`
4. **Maintain contrast** - ensure text remains readable in all themes
5. **Consider hover states** - use the provided hover variables for interactive elements

## Testing Themes

To test different themes quickly:

1. Change the import in `index.css`
2. Save the file
3. The hot reload will apply the new theme immediately
4. Check all pages and components for consistency
