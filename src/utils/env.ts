// Environment variables utility with type safety
export const env = {
  // Site configuration
  SITE_NAME: import.meta.env.VITE_SITE_NAME || "Sanrado Techsolutions LLP",
  SITE_URL: import.meta.env.VITE_SITE_URL || "https://sanrado.com",

  // Company information
  COMPANY_EMAIL: import.meta.env.VITE_COMPANY_EMAIL || "<EMAIL>",
  COMPANY_PHONE: import.meta.env.VITE_COMPANY_PHONE || "+91 99436 22335",
  COMPANY_ADDRESS:
    import.meta.env.VITE_COMPANY_ADDRESS ||
    "1/184, Salaithottam, Kasba Ayyampalayam, Palladam, Tiruppur, Tamil Nadu 641662, India",

  // API configuration
  API_URL: import.meta.env.VITE_API_URL || "https://api.sanrado.com",

  // Analytics
  GA_TRACKING_ID: import.meta.env.VITE_GA_TRACKING_ID || "",
  HOTJAR_ID: import.meta.env.VITE_HOTJAR_ID || "",

  // Feature flags
  ENABLE_ANALYTICS: import.meta.env.VITE_ENABLE_ANALYTICS === "true",
  ENABLE_CHAT: import.meta.env.VITE_ENABLE_CHAT === "true",

  // Environment
  DEV: import.meta.env.DEV,
  PROD: import.meta.env.PROD,
} as const;

// Type for environment variables
export type EnvConfig = typeof env;
