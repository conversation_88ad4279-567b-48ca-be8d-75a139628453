/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_GOOGLE_MAPS_API_KEY: string;
  readonly VITE_EMAILJS_PUBLIC_KEY: string;
  readonly VITE_EMAILJS_SERVICE_ID: string;
  readonly VITE_EMAILJS_TEMPLATE_ID: string;
  readonly VITE_USE_SERVER_EMAIL: string;
  readonly VITE_AWS_ACCESS_KEY_ID: string;
  readonly VITE_AWS_SECRET_ACCESS_KEY: string;
  readonly VITE_SENDGRID_API_KEY: string;
  readonly VITE_SITE_NAME: string;
  readonly VITE_SITE_URL: string;
  readonly VITE_COMPANY_EMAIL: string;
  readonly VITE_COMPANY_PHONE: string;
  readonly VITE_COMPANY_ADDRESS: string;
  readonly VITE_API_URL: string;
  readonly VITE_GA_TRACKING_ID: string;
  readonly VITE_HOTJAR_ID: string;
  readonly VITE_ENABLE_ANALYTICS: string;
  readonly VITE_ENABLE_CHAT: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
