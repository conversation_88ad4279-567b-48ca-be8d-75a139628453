export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      fontFamily: {
        sans: ["Atkinson Hyperlegible", "system-ui", "sans-serif"],
        heading: ["Lato", "system-ui", "sans-serif"],
      },
      colors: {
        theme: {
          primary: "var(--color-primary)",
          secondary: "var(--color-secondary)",
          accent: "var(--color-accent)",
          light: "var(--color-light)",
          text: {
            primary: "var(--color-text-primary)",
            secondary: "var(--color-text-secondary)",
            light: "var(--color-text-light)",
          },
          bg: {
            primary: "var(--color-background)",
            alt: "var(--color-background-alt)",
          },
          border: "var(--color-border)",
          hover: {
            primary: "var(--color-hover-primary)",
            secondary: "var(--color-hover-secondary)",
          },
        },
      },
      animation: {
        "fade-in": "fadeIn 0.5s ease-in-out",
        "slide-up": "slideUp 0.3s ease-out",
      },
      keyframes: {
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        slideUp: {
          "0%": { transform: "translateY(10px)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
      },
    },
  },
};
